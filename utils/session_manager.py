"""Session management for storing user preferences"""

from datetime import datetime

# Simple in-memory session storage
# In a production environment, you might use Redis or a database
_sessions = {}

def get_session(session_id="default"):
    """Get or create a session for the given ID"""
    if session_id not in _sessions:
        _sessions[session_id] = {
            "auto_confirm_sql": True,  # Default: auto-confirm enabled
            "query_history": []  # Track SQL query history
        }
    return _sessions[session_id]

def enable_auto_confirm(session_id="default"):
    """Enable auto-confirm for SQL queries"""
    session = get_session(session_id)
    session["auto_confirm_sql"] = True
    return session

def disable_auto_confirm(session_id="default"):
    """Disable auto-confirm for SQL queries"""
    session = get_session(session_id)
    session["auto_confirm_sql"] = False
    return session

def get_auto_confirm_status(session_id="default"):
    """Get the current auto-confirm status"""
    session = get_session(session_id)
    return session["auto_confirm_sql"]

def add_query_to_session(query, session_id="default"):
    """Add a SQL query to the session history"""
    if not query or not isinstance(query, str):
        return None

    session = get_session(session_id)
    executed_at = datetime.now()
    query_entry = {
        "query": query.strip(),  # Remove leading/trailing whitespace
        "timestamp": executed_at.isoformat(),
        "executed_at": executed_at
    }
    session["query_history"].append(query_entry)

    # Keep only the last 50 queries to prevent memory issues
    if len(session["query_history"]) > 50:
        session["query_history"] = session["query_history"][-50:]

    return query_entry

def get_query_history(session_id="default"):
    """Get the query history for a session"""
    session = get_session(session_id)
    return session["query_history"]

# Initialize default session with auto-confirm enabled
enable_auto_confirm()
