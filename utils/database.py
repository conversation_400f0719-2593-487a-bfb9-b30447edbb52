import psycopg2
import psycopg2.extras
from config import Config

def get_db_connection():
    """Create a connection to the PostgreSQL database"""
    conn = psycopg2.connect(
        host=Config.DATABASE_HOST,
        database=Config.DATABASE_NAME,
        user=Config.DATABASE_USER,
        password=Config.DATABASE_PASSWORD,
        port=Config.DATABASE_PORT
    )
    # Enable autocommit
    conn.autocommit = True
    return conn

def execute_query(query, params=None, fetch_one=False):
    """Execute a query and return results"""
    print(f"Database executing: {query}")
    if params:
        print(f"With parameters: {params}")
        
    conn = get_db_connection()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(query, params)
            if cur.description:  # Check if the query returns data
                if fetch_one:
                    result = dict(cur.fetchone()) if cur.rowcount > 0 else None
                    print(f"Fetched single row: {result is not None}")
                    return result
                results = [dict(row) for row in cur.fetchall()]
                print(f"Fetched {len(results)} rows")
                return results
            print("Query executed with no result set")
            return None
    except Exception as e:
        print(f"Database error: {str(e)}")
        raise
    finally:
        conn.close()

# Make sure these functions are exported
__all__ = ['get_db_connection', 'execute_query']
