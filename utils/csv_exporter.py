import os
import csv
import uuid
import datetime
import json
import sys
from pathlib import Path

EXPORTS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "exports")
# Create exports directory if it doesn't exist
Path(EXPORTS_DIR).mkdir(parents=True, exist_ok=True)

MAX_DIRECT_RESULTS = 20  # Threshold for when to use CSV instead of direct JSON response
MAX_RESPONSE_SIZE_MB = 0.01  # Maximum response size in MB before forcing CSV export

def calculate_response_size_mb(results):
    """
    Calculate the estimated size of the JSON response in MB

    Args:
        results (list): List of dictionaries with query results

    Returns:
        float: Estimated size in MB
    """
    if not results:
        return 0.0

    try:
        # For large datasets, sample a few rows to estimate size instead of serializing everything
        sample_size = min(len(results), 10)  # Sample up to 10 rows
        sample_results = results[:sample_size]

        # Convert sample to JSON string to get accurate size estimation
        json_str = json.dumps(sample_results, ensure_ascii=False, default=str)
        sample_size_bytes = sys.getsizeof(json_str)

        # Estimate total size based on sample
        if sample_size > 0:
            avg_row_size = sample_size_bytes / sample_size
            total_size_bytes = avg_row_size * len(results)
            # Add overhead for JSON array structure
            total_size_bytes += len(results) * 2  # Commas and brackets
        else:
            total_size_bytes = 0

        size_mb = total_size_bytes / (1024 * 1024)  # Convert to MB
        return size_mb
    except Exception as e:
        # Fallback: rough estimation based on string representation
        try:
            # Sample approach for fallback too
            sample_size = min(len(results), 5)
            sample_chars = sum(len(str(row)) for row in results[:sample_size])
            if sample_size > 0:
                avg_chars_per_row = sample_chars / sample_size
                total_chars = avg_chars_per_row * len(results)
            else:
                total_chars = 0
            # Rough estimation: 1 character ≈ 1 byte, plus JSON overhead
            estimated_bytes = total_chars * 1.5  # 50% overhead for JSON formatting
            return estimated_bytes / (1024 * 1024)
        except Exception:
            # Ultimate fallback: assume 1KB per row
            return (len(results) * 1024) / (1024 * 1024)

def should_export_to_csv(results):
    """
    Determine whether results should be exported to CSV based on row count and size

    Args:
        results (list): List of dictionaries with query results

    Returns:
        tuple: (should_export: bool, reason: str, size_mb: float)
    """
    if not results:
        return False, "No results", 0.0

    row_count = len(results)
    size_mb = calculate_response_size_mb(results)

    if row_count > MAX_DIRECT_RESULTS:
        return True, f"Row count ({row_count}) exceeds limit ({MAX_DIRECT_RESULTS})", size_mb

    if size_mb > MAX_RESPONSE_SIZE_MB:
        return True, f"Response size ({size_mb:.2f} MB) exceeds limit ({MAX_RESPONSE_SIZE_MB} MB)", size_mb

    return False, f"Within limits (rows: {row_count}, size: {size_mb:.2f} MB)", size_mb

def export_to_csv(results, query=None):
    """
    Export query results to CSV file

    Args:
        results (list): List of dictionaries with query results
        query (str, optional): SQL query for reference (not included in the CSV)

    Returns:
        tuple: (filename, file_path, row_count)
    """
    if not results:
        return None, None, 0

    if not isinstance(results, list) or not all(isinstance(row, dict) for row in results):
        raise ValueError("Results must be a list of dictionaries")

    try:
        # Generate unique filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        filename = f"query_results_{timestamp}_{unique_id}.csv"
        file_path = os.path.join(EXPORTS_DIR, filename)

        # Ensure exports directory exists
        os.makedirs(EXPORTS_DIR, exist_ok=True)

        # Write results to CSV with UTF-8 encoding
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = list(results[0].keys())  # Convert to list for consistency
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for row in results:
                # Handle None values and ensure all values are properly encoded
                clean_row = {}
                for key in fieldnames:  # Use fieldnames to ensure consistent column order
                    value = row.get(key)  # Use .get() to handle missing keys
                    if value is None:
                        clean_row[key] = ''
                    else:
                        # Handle special characters and ensure string conversion
                        try:
                            clean_row[key] = str(value)
                        except Exception:
                            clean_row[key] = repr(value)  # Fallback for problematic values
                writer.writerow(clean_row)

        # Verify file was created successfully
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            raise Exception("CSV file was not created properly")

        return filename, file_path, len(results)

    except Exception as e:
        # Clean up partial file if it exists
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except:
                pass
        raise Exception(f"Failed to export CSV: {str(e)}")

def cleanup_old_exports(max_age_hours=24):
    """Delete CSV files older than specified hours"""
    if not os.path.exists(EXPORTS_DIR):
        return

    cutoff = datetime.datetime.now() - datetime.timedelta(hours=max_age_hours)

    try:
        for file in os.listdir(EXPORTS_DIR):
            # Security: Only process CSV files and avoid directory traversal
            if file.endswith('.csv') and not file.startswith('.') and '/' not in file and '\\' not in file:
                file_path = os.path.join(EXPORTS_DIR, file)
                # Additional security check: ensure file is actually in our exports directory
                if not os.path.commonpath([file_path, EXPORTS_DIR]) == EXPORTS_DIR:
                    continue

                if os.path.isfile(file_path):
                    file_modified = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_modified < cutoff:
                        try:
                            os.remove(file_path)
                            print(f"Removed old export file: {file}")
                        except Exception as e:
                            print(f"Failed to remove old file {file}: {str(e)}")
    except Exception as e:
        print(f"Error during cleanup: {str(e)}")