"""
Utility modules for the application.

This package contains utility modules for database access, session management, and CSV export.
"""

from utils.database import get_db_connection, execute_query
from utils.session_manager import get_session, enable_auto_confirm, disable_auto_confirm, get_auto_confirm_status, add_query_to_session, get_query_history
from utils.csv_exporter import export_to_csv, cleanup_old_exports, should_export_to_csv, calculate_response_size_mb, MAX_DIRECT_RESULTS, MAX_RESPONSE_SIZE_MB

__all__ = [
    "get_db_connection", "execute_query",
    "get_session", "enable_auto_confirm", "disable_auto_confirm", "get_auto_confirm_status", "add_query_to_session", "get_query_history",
    "export_to_csv", "cleanup_old_exports", "should_export_to_csv", "calculate_response_size_mb", "MAX_DIRECT_RESULTS", "MAX_RESPONSE_SIZE_MB"
]
