class Config:
    DEBUG = False
    TESTING = False
    SECRET_KEY = "your-secret-key"

    # Email configuration
    SMTP_SERVER = "smtp.gmail.com"
    SMTP_PORT = 587
    IMAP_SERVER = "imap.gmail.com"
    IMAP_PORT = 993
    EMAIL_ACCOUNT = "<EMAIL>"
    EMAIL_PASSWORD = "hkgp gjtn hppf svys"

    # API keys
    GEMINI_API_KEY = "AIzaSyC4OqoBZQQUIbxDFD4sDIFTt2z3T34mjdM"
    DEEPSEEK_API_KEY = "***********************************"

    # Database (placeholder)
    DATABASE_HOST = "localhost"
    DATABASE_NAME = "mydb"
    DATABASE_USER = "xiuli"
    DATABASE_PASSWORD = "73Lxf1017"
    DATABASE_PORT = 5432

    PM_AUTH_PASSWORD = "optix1234"

    TASK_LEADER_EMAILS = {
        "<EMAIL>": {
            "name": "<PERSON> Siever<PERSON> Nielsen",
            "tasks": ["T1.1", "T1.2", "T1.3", "T1.4", "T3.5", "T5.8", "T6.1", "T6.2", "T6.3", "T6.4"]
        },
        "<EMAIL>": {
            "name": "Amir Vadiee",
            "tasks": ["T2.1", "T3.2", "T5.2"]
        },
        "<EMAIL>": {
            "name": "Madalin Silion",
            "tasks": ["T2.2"]
        },
        "<EMAIL>": {
            "name": "Yang Liu",
            "tasks": ["T2.3"]
        },
        "<EMAIL>": {
            "name": "Xiufeng Liu",
            "tasks": ["T3.1"]
        },
        "<EMAIL>": {
            "name": "Brian O'Regan",
            "tasks": ["T3.3", "T4.3"]
        },
        "<EMAIL>": {
            "name": "Dud Charleville",
            "tasks": ["T3.4", "T4.2", "T5.1"]
        },
        "<EMAIL>": {
            "name": "Brian O'Regan",
            "tasks": ["T4.1"]
        },
        "<EMAIL>": {
            "name": "Cigdem Karadag",
            "tasks": ["T4.4", "T5.7"]
        },
        "<EMAIL>": {
            "name": "Jakob Jespersen",
            "tasks": ["T5.3"]
        },
        "<EMAIL>": {
            "name": "Mahmut Sami Büker",
            "tasks": ["T5.4"]
        },
        "<EMAIL>": {
            "name": "Gerfried Cebrat",
            "tasks": ["T5.5", "T5.6"]
        }
        }


    # Additional stakeholders who should receive shared reports
    REPORT_DISTRIBUTION_CC = [
        {
            "name": "Francoise Qvistgaard",
            "email": "<EMAIL>",
            "role": "Project Administrator"
        }
    ]

    # Work Packages and Tasks Mapping
    WP_TASK_STRUCTURE = {
        "WP1": {
            "title": "Project Oversight and Coordination",
            "tasks": {
                "T1.1": "Project kick-off and consortium agreement",
                "T1.2": "Project monitoring and reporting",
                "T1.3": "Project risk management",
                "T1.4": "Project quality assurance"
            }
        },
        "WP2": {
            "title": "Integrating and Optimising for Positive-Energy Buildings",
            "tasks": {
                "T2.1": "Solar PV in building design",
                "T2.2": "Energy flexibility",
                "T2.3": "Modelling surplus scenarios in positive-energy buildings"
            }
        },
        "WP3": {
            "title": "Scalable Modelling of Positive-Energy Districts",
            "tasks": {
                "T3.1": "Scalable positive-energy district modelling",
                "T3.2": "Demand response and flexibility services toolkit",
                "T3.3": "Policy and regulatory guidelines for energy districts",
                "T3.4": "Distributed ledger solutions for energy communities",
                "T3.5": "Regulation of energy communities"
            }
        },
        "WP4": {
            "title": "Implementation of District-Centric Energy Management and Trading Solutions",
            "tasks": {
                "T4.1": "Energy Communities",
                "T4.2": "Community Technology",
                "T4.3": "Transactive Energy",
                "T4.4": "Hydrogen Storage"
            }
        },
        "WP5": {
            "title": "Real-World Demonstration, Testing and Exploitation Strategies",
            "tasks": {
                "T5.1": "Case 1 – Peer-to-Peer energy trading (MEGA)",
                "T5.2": "Case 2 – Grid balancing (MDU, Sally-R)",
                "T5.3": "Case 3 – Municipality-based energy opportunities (LTM)",
                "T5.4": "Case 4 – Positive energy building (Innorma)",
                "T5.5": "Case 5 – Power-to-heat systems (EUC)",
                "T5.6": "Case 6 – Advanced solar buildings (BEIA)",
                "T5.7": "Case 7 – Digital twin of fuel cell systems (TÜBİTAK, ICSI)",
                "T5.8": "Evaluation and exploitation of project outcomes"
            }
        },
        "WP6": {
            "title": "Reporting and Knowledge Community",
            "tasks": {
                "T6.1": "Reporting",
                "T6.2": "Contribution to formative evaluation",
                "T6.3": "Contribution to Knowledge Community activities",
                "T6.4": "Knowledge dissemination and transfer"
            }
        }
    }



class DevelopmentConfig(Config):
    DEBUG = True


class ProductionConfig(Config):
    pass


class TestingConfig(Config):
    TESTING = True
