from models.policy import Policy
from utils.database import execute_query

class PolicyService:
    def __init__(self):
        self.table_name = "policy_data_processed"
    
    def get_all(self):
        """Get all policies from the database"""
        query = f"SELECT * FROM {self.table_name} ORDER BY id"
        results = execute_query(query)
        return [Policy.from_dict(row) for row in results]
    
    def get_by_id(self, id):
        """Get a single policy by ID"""
        query = f"SELECT * FROM {self.table_name} WHERE id = %s"
        result = execute_query(query, (id,), fetch_one=True)
        return Policy.from_dict(result) if result else None
    
    def search(self, filters=None):
        """Search policies with filters"""
        query = f"SELECT * FROM {self.table_name} WHERE 1=1"
        params = []
        
        if filters:
            if 'policy_title' in filters and filters['policy_title']:
                query += " AND policy_title ILIKE %s"
                params.append(f"%{filters['policy_title']}%")
                
            if 'is_valid_policy' in filters and filters['is_valid_policy'] is not None:
                query += " AND is_valid_policy = %s"
                params.append(filters['is_valid_policy'])
                
            if 'is_low_carbon' in filters and filters['is_low_carbon'] is not None:
                query += " AND is_low_carbon = %s"
                params.append(filters['is_low_carbon'])
                
            if 'policy_level' in filters and filters['policy_level']:
                query += " AND policy_level = %s"
                params.append(filters['policy_level'])
                
            if 'policy_strength' in filters and filters['policy_strength'] is not None:
                query += " AND policy_strength = %s"
                params.append(filters['policy_strength'])
                
            if 'is_related_to_city' in filters and filters['is_related_to_city'] is not None:
                query += " AND is_related_to_city = %s"
                params.append(filters['is_related_to_city'])
                
            if 'target_sectors' in filters and filters['target_sectors']:
                query += " AND target_sectors ILIKE %s"
                params.append(f"%{filters['target_sectors']}%")
                
            if 'issuing_department' in filters and filters['issuing_department']:
                query += " AND issuing_department ILIKE %s"
                params.append(f"%{filters['issuing_department']}%")
            
        query += " ORDER BY id"
        results = execute_query(query, params)
        return [Policy.from_dict(row) for row in results]
    
    def execute_custom_query(self, query):
        """Execute a custom SQL query directly (SELECT queries only)"""
        if not query.lower().strip().startswith('select'):
            raise ValueError("Only SELECT queries are allowed")
            
        try:
            print(f"Executing database query: {query}")
            results = execute_query(query)
            if results:
                print(f"Query returned {len(results)} rows with columns: {list(results[0].keys())}")
            else:
                print("Query returned no results")
            return results
        except Exception as e:
            print(f"Query execution failed: {str(e)}")
            # Re-raise with more helpful message
            raise Exception(f"SQL query execution failed: {str(e)}")

# Ensure the class is exported
__all__ = ['PolicyService']
