"""
Main application module.

This module creates and configures the Flask application.
"""

import os
import sys
import traceback
import importlib
from flask import Flask

# Add the parent directory to sys.path to make imports work
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def create_app():
    """
    Create and configure the Flask application.

    Returns:
        Flask: The configured Flask application.
    """
    # Force reload modules to avoid caching issues
    modules_to_reload = [
        "api.routes",
        "api.resources.policy",
        "api.resources.optix",
        "services.policy",
        "utils.database",
        "utils.session_manager",
        "utils.csv_exporter"
    ]

    for module_name in modules_to_reload:
        if module_name in sys.modules:
            print(f"Reloading {module_name} module")
            importlib.reload(sys.modules[module_name])

    # Create Flask app
    app = Flask(__name__)

    # Set configuration
    app.config["ENV"] = "development"
    app.config["DEBUG"] = True
    app.config["SECRET_KEY"] = "your-secret-key"
    app.config["JSON_SORT_KEYS"] = False  # Preserve key order in JSON responses
    app.url_map.strict_slashes = False    # Handle trailing slashes flexibly

    # Test database connection on startup
    try:
        import utils.database
        conn = utils.database.get_db_connection()
        conn.close()
        print("Database connection successful!")

        # Ensure auto-confirm is enabled by default
        from utils.session_manager import enable_auto_confirm
        enable_auto_confirm()
        print("SQL auto-confirm enabled by default")
    except Exception as e:
        print(f"⚠️ Database connection failed: {e}")
        traceback.print_exc()

    # Root route
    @app.route('/')
    def index():
        """Return application information and available endpoints."""
        return {
            "message": "Application is running",
            "policy_endpoints": [
                "/api/v1/policies - Get all policies with optional filters",
                "/api/v1/policies/123 - Get policy by ID",
                "/api/v1/sql - Execute custom SQL queries (POST or GET)"
            ],
            "optix_endpoints": [
                "/api/v1/send-task-update-request - Send task update requests to all task leaders",
                "/api/v1/send-email - Send an email to one or more recipients",
                "/api/v1/generate-task-status-report - Generate a task status report from email replies",
                "/api/v1/check-latest-report - Check if a new report has been completed",
                "/api/v1/task-status/<task_id> - Check the status of an asynchronous task",
                "/api/v1/send-report-link - Send a report link to all task leaders",
                "/api/v1/delete-all-reports - Delete all existing reports (requires authentication)"
            ],
            "settings_endpoints": [
                "/api/v1/settings/auto-confirm - Get auto-confirm status",
                "/api/v1/settings/auto-confirm/enable - Enable auto-confirm",
                "/api/v1/settings/auto-confirm/disable - Disable auto-confirm"
            ]
        }

    # Import and register routes
    try:
        # Force import the routes module
        from api.routes import register_routes
        register_routes(app)
        print("Routes registered successfully")
    except Exception as e:
        print(f"Error registering routes: {e}")
        traceback.print_exc()

    return app

if __name__ == "__main__":
    """Run the application when executed directly."""
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the REST API server")
    parser.add_argument("--server.port", type=int, default=8503, help="Port to run the server on")
    parser.add_argument("--server.address", default="0.0.0.0", help="Address to bind the server to")
    args = parser.parse_args()

    # Extract host and port from arguments
    port = getattr(args, "server.port")
    host = getattr(args, "server.address")

    print("Starting application server...")
    app = create_app()
    print(f"Server running at: http://{host}:{port}/")
    print("Press CTRL+C to quit")

    app.run(host=host, port=port, use_reloader=False)
