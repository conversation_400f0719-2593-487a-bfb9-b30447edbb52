from datetime import date, datetime

class Policy:
    def __init__(self, id=None, policy_title=None, is_valid_policy=None, 
                is_low_carbon=None, policy_level=None, is_related_to_city=None,
                target_sectors=None, policy_strength=None, policy_strength_justification=None,
                issuing_department=None, announcement_date=None, processed_at=None):
        self.id = id
        self.policy_title = policy_title
        self.is_valid_policy = is_valid_policy
        self.is_low_carbon = is_low_carbon
        self.policy_level = policy_level
        self.is_related_to_city = is_related_to_city
        self.target_sectors = target_sectors
        self.policy_strength = policy_strength
        self.policy_strength_justification = policy_strength_justification
        self.issuing_department = issuing_department
        self.announcement_date = announcement_date
        self.processed_at = processed_at
    
    @classmethod
    def from_dict(cls, data):
        """Create a Policy object from a dictionary"""
        return cls(
            id=data.get('id'),
            policy_title=data.get('policy_title'),
            is_valid_policy=data.get('is_valid_policy'),
            is_low_carbon=data.get('is_low_carbon'),
            policy_level=data.get('policy_level'),
            is_related_to_city=data.get('is_related_to_city'),
            target_sectors=data.get('target_sectors'),
            policy_strength=data.get('policy_strength'),
            policy_strength_justification=data.get('policy_strength_justification'),
            issuing_department=data.get('issuing_department'),
            announcement_date=data.get('announcement_date'),
            processed_at=data.get('processed_at')
        )
    
    def to_dict(self):
        """Convert Policy object to dictionary"""
        result = {
            "id": self.id,
            "policy_title": self.policy_title,
            "is_valid_policy": self.is_valid_policy,
            "is_low_carbon": self.is_low_carbon,
            "policy_level": self.policy_level,
            "is_related_to_city": self.is_related_to_city,
            "target_sectors": self.target_sectors,
            "policy_strength": self.policy_strength,
            "policy_strength_justification": self.policy_strength_justification,
            "issuing_department": self.issuing_department
        }
        
        # Handle date/datetime conversions
        if self.announcement_date:
            if isinstance(self.announcement_date, date):
                result['announcement_date'] = self.announcement_date.isoformat()
            else:
                result['announcement_date'] = self.announcement_date
                
        if self.processed_at:
            if isinstance(self.processed_at, datetime):
                result['processed_at'] = self.processed_at.isoformat()
            else:
                result['processed_at'] = self.processed_at
                
        return result
