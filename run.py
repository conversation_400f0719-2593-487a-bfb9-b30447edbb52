#!/usr/bin/env python3
"""
Run script for the REST API application.

This script runs the Flask application with the correct Python path.
"""
import os
import sys
import traceback

# Add the current directory to Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    from app import create_app

    if __name__ == "__main__":
        port = 8503
        host = "0.0.0.0"

        print("Starting REST API server...")
        app = create_app()
        print(f"Server running at: http://{host}:{port}/")
        print(f"Try accessing: http://{host}:{port}/api/v1/policies")
        print(f"Or: http://{host}:{port}/api/v1/sql?query=SELECT+COUNT(*)+FROM+policy_data_processed")
        print("Press CTRL+C to quit")

        app.run(debug=True, host=host, port=port)
except Exception as e:
    print(f"Error: {e}")
    traceback.print_exc()
    sys.exit(1)
