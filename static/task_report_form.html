<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OPTIX Task Progress Report Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 5px solid #3498db;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .debug-info {
            margin-top: 20px;
            padding: 10px;
            background-color: #f1f1f1;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>OPTIX Task Progress Report Generator</h1>
    <p>Use this form to generate a task progress report from email replies.</p>

    <form id="reportForm">
        <div class="form-group">
            <label for="project_name">Project Name:</label>
            <input type="text" id="project_name" name="project_name" value="OPTIX" required>
        </div>

        <div class="form-group">
            <label for="days_back">Days to Look Back:</label>
            <input type="number" id="days_back" name="days_back" value="30" min="1" max="365" required>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="force_refresh" name="force_refresh" value="true">
                Force Refresh (Ignore cached reports)
            </label>
            <p class="help-text" style="font-size: 0.9em; color: #666; margin-top: 5px;">
                By default, reports are cached for 1 hour to reduce server load and email fetching.
            </p>
        </div>

        <div class="form-group">
            <button type="submit">Generate Report</button>
        </div>
    </form>

    <div id="loading" class="loading">
        <p>Generating report, please wait...</p>
    </div>

    <div id="result" class="result" style="display: none;">
        <h2>Generated Report</h2>
        <div id="reportContent"></div>
        <p><a id="downloadLink" href="#" target="_blank">Download PDF Report</a></p>
        <div id="debugInfo" class="debug-info" style="display: none;"></div>
    </div>

    <script>
        // Function to poll task status
        function pollTaskStatus(taskId, pollInterval = 5000) {
            document.getElementById('loading').innerHTML = '<p>Generating report, please wait... <span id="statusMessage">Checking status...</span></p>';

            // Function to check task status
            function checkStatus() {
                fetch(`/api/v1/task-status/${taskId}`)
                    .then(response => response.json())
                    .then(data => {
                        // Update status message
                        document.getElementById('statusMessage').textContent = `Status: ${data.status}`;

                        if (data.status === 'completed') {
                            // Task completed successfully
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('result').style.display = 'block';

                            const results = data.results;

                            if (results.summary) {
                                document.getElementById('reportContent').innerHTML = `<pre>${results.summary}</pre>`;
                                document.getElementById('downloadLink').href = results.download_url;
                                document.getElementById('downloadLink').style.display = 'block';
                            } else if (results.message) {
                                document.getElementById('reportContent').innerHTML = `<p>${results.message}</p>`;
                                document.getElementById('downloadLink').style.display = 'none';
                            }

                        } else if (data.status === 'failed') {
                            // Task failed
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('result').style.display = 'block';

                            const errorMsg = data.results && data.results.error ? data.results.error : 'Unknown error occurred';
                            document.getElementById('reportContent').innerHTML = `<p class="error">Error: ${errorMsg}</p>`;
                            document.getElementById('downloadLink').style.display = 'none';

                            // Show debug info if available
                            if (data.debug) {
                                const debugInfo = document.getElementById('debugInfo');
                                debugInfo.innerHTML = '<h3>Debug Information:</h3><ul>' +
                                    data.debug.map(info => `<li>${info}</li>`).join('') +
                                    '</ul>';
                                debugInfo.style.display = 'block';
                            }

                        } else {
                            // Task still in progress, continue polling
                            setTimeout(checkStatus, pollInterval);
                        }
                    })
                    .catch(error => {
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('result').style.display = 'block';
                        document.getElementById('reportContent').innerHTML = `<p class="error">Error checking task status: ${error.message}</p>`;
                        document.getElementById('downloadLink').style.display = 'none';
                    });
            }

            // Start polling
            checkStatus();
        }

        document.getElementById('reportForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';

            // Create form data
            const formData = new FormData();
            formData.append('project_name', document.getElementById('project_name').value);
            formData.append('days_back', document.getElementById('days_back').value);

            // Add force refresh if checked
            const forceRefresh = document.getElementById('force_refresh').checked;
            if (forceRefresh) {
                formData.append('force_refresh', 'true');
            }

            // Send request
            fetch('/api/v1/generate-task-status-report', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    // Handle error
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('result').style.display = 'block';
                    document.getElementById('reportContent').innerHTML = `<p class="error">Error: ${data.error}</p>`;
                    document.getElementById('downloadLink').style.display = 'none';

                    // Show debug info if available
                    if (data.debug) {
                        const debugInfo = document.getElementById('debugInfo');
                        debugInfo.innerHTML = '<h3>Debug Information:</h3><ul>' +
                            data.debug.map(info => `<li>${info}</li>`).join('') +
                            '</ul>';
                        debugInfo.style.display = 'block';
                    }
                } else if (data.task_id) {
                    // This is an async task, start polling for status
                    pollTaskStatus(data.task_id);
                } else if (data.cached) {
                    // This is a cached result, display immediately
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('result').style.display = 'block';
                    document.getElementById('reportContent').innerHTML = `<pre>${data.summary}</pre>`;
                    document.getElementById('reportContent').innerHTML += `<p><em>Using cached report (${data.cache_age_minutes} minutes old)</em></p>`;
                    document.getElementById('downloadLink').href = data.download_url;
                    document.getElementById('downloadLink').style.display = 'block';
                } else {
                    // Direct response with data
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('result').style.display = 'block';
                    document.getElementById('reportContent').innerHTML = `<pre>${data.summary}</pre>`;
                    document.getElementById('downloadLink').href = data.download_url;
                    document.getElementById('downloadLink').style.display = 'block';
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('result').style.display = 'block';
                document.getElementById('reportContent').innerHTML = `<p class="error">Error: ${error.message}</p>`;
                document.getElementById('downloadLink').style.display = 'none';
            });
        });
    </script>
</body>
</html>
