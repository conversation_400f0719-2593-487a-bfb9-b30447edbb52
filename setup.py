"""
Setup script for the REST API service.

This script installs the REST API service as a Python package.
"""

from setuptools import setup, find_packages

setup(
    name="rest-api-service",
    version="0.1.0",
    description="REST API for policy data and OPTIX project management",
    author="OPTIX Team",
    packages=find_packages(),
    install_requires=[
        "flask",
        "psycopg2-binary",
        "google-generativeai",
        "markdown2",
        "pdfkit",
    ],
    python_requires=">=3.8",
)
