"""
Policy resource module.

This module contains the PolicyResource class for handling policy-related API endpoints.
"""

from flask import request, jsonify
from flask.views import MethodView
from models.policy import Policy
from services.policy import PolicyService
from utils.session_manager import get_session, add_query_to_session
from utils.csv_exporter import export_to_csv, cleanup_old_exports, should_export_to_csv, MAX_DIRECT_RESULTS, MAX_RESPONSE_SIZE_MB

class PolicyResource(MethodView):
    """Resource class for policy-related endpoints."""

    def get(self, policy_id=None):
        """
        Get a policy or list of policies.

        Args:
            policy_id (int, optional): The ID of the policy to retrieve.

        Returns:
            A JSON response with the policy data.
        """
        service = PolicyService()
        
        if policy_id:
            # Get a single policy by ID
            policy = service.get_by_id(policy_id)
            if policy:
                return jsonify(policy.to_dict())
            return {"message": "Policy not found"}, 404
        else:
            # Get all policies with optional filters
            filters = {}
            for param in request.args:
                if param in ['is_valid_policy', 'is_low_carbon', 'is_related_to_city']:
                    # Convert string to boolean
                    filters[param] = request.args.get(param).lower() == 'true'
                elif param == 'policy_strength':
                    # Convert string to integer
                    try:
                        filters[param] = int(request.args.get(param))
                    except ValueError:
                        return {"error": f"Invalid value for {param}. Must be an integer."}, 400
                else:
                    filters[param] = request.args.get(param)
            
            policies = service.search(filters)
            return jsonify([policy.to_dict() for policy in policies])
    
    def execute_sql(self, query, session_id='default'):
        """
        Execute a custom SQL query.

        Args:
            query (str): The SQL query to execute.
            session_id (str): The session ID for tracking query history.

        Returns:
            A JSON response with the query results or a download link for large result sets.
        """
        try:
            # Input validation
            if not query or not isinstance(query, str):
                return {"error": "Query must be a non-empty string"}, 400

            query = query.strip()
            if not query:
                return {"error": "Query cannot be empty or whitespace only"}, 400

            if not session_id or not isinstance(session_id, str):
                session_id = 'default'

            # Add the query to the session history
            add_query_to_session(query, session_id)

            # Execute the query
            service = PolicyService()
            results = service.execute_custom_query(query)

            # Check if results are empty
            if not results:
                return jsonify({
                    "results": [],
                    "query": query,
                    "row_count": 0,
                    "message": "Query executed successfully but returned no results."
                })

            row_count = len(results)

            # Check if results should be exported to CSV based on size and row count
            should_export, reason, size_mb = should_export_to_csv(results)

            if should_export:
                try:
                    # Export results to CSV first
                    filename, file_path, exported_count = export_to_csv(results, query)

                    # Clean up old exports after successful creation (to avoid race conditions)
                    try:
                        cleanup_old_exports(max_age_hours=24)
                    except Exception as cleanup_error:
                        # Log cleanup error but don't fail the request
                        print(f"Warning: Cleanup failed: {cleanup_error}")
                except Exception as export_error:
                    return {"error": f"Failed to export results to CSV: {str(export_error)}"}, 500

                if filename:
                    download_url = f"https://optix.scicloud.site/api/v1/downloads/{filename}"
                    return jsonify({
                        "query": query,
                        "row_count": row_count,
                        "response_size_mb": round(size_mb, 2),
                        "exported_to_csv": True,
                        "download_url": download_url,
                        "filename": filename,
                        "message": f"Results exported to CSV file for download. Reason: {reason}",
                        "export_reason": reason,
                        "limits": {
                            "max_direct_results": MAX_DIRECT_RESULTS,
                            "max_response_size_mb": MAX_RESPONSE_SIZE_MB
                        }
                    })
                else:
                    return {"error": "Failed to export results to CSV"}, 500

            # Return results directly if under the limits
            return jsonify({
                "results": results,
                "query": query,
                "row_count": row_count,
                "response_size_mb": round(size_mb, 2),
                "exported_to_csv": False,
                "message": f"Query executed successfully and returned {row_count} rows ({size_mb:.2f} MB).",
                "within_limits": reason
            })

        except Exception as e:
            return {"error": str(e)}, 400
