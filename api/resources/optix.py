"""
OPTIX project management resource module.

This module contains the OptixResource class for handling OPTIX project-related API endpoints.
"""

import os
import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.header import decode_header
import datetime
from datetime import datetime, timedelta
import time
import threading
import uuid
import google.generativeai as genai
import markdown2
import pdfkit
import re
from flask import request, send_from_directory, jsonify
from flask.views import MethodView
from config import Config

# Task tracking for async operations
task_status = {}  # Stores the status of each task: "pending", "processing", "completed", "failed"
task_results = {}  # Stores the results of completed tasks
task_debug = {}   # Stores debug information for tasks

# Global flag to track if a report generation is in progress
report_generation_in_progress = False
current_report_task_id = None  # Stores the task ID of the currently running report generation

def generate_report_async(task_id, resource, project_name, days_back, force_refresh):
    """
    Background task to generate a report asynchronously.

    Args:
        task_id (str): The unique ID for this task
        resource (OptixResource): The OptixResource instance
        project_name (str): The project name
        days_back (int): Number of days to look back for emails
        force_refresh (bool): Whether to force a refresh or use cache
    """
    global report_generation_in_progress, current_report_task_id

    try:
        # Set the global flag to indicate that a report generation is in progress
        report_generation_in_progress = True
        current_report_task_id = task_id

        task_status[task_id] = "processing"
        task_debug[task_id] = []

        # Check if a report has been generated within the last hour
        if not force_refresh:
            try:
                # Get all PDF files in the exports directory
                report_files = [f for f in os.listdir(resource.exports_dir)
                               if f.startswith("OPTIX_Progress_Report_") and f.endswith(".pdf")]

                if report_files:
                    # Sort by modification time (newest first)
                    report_files.sort(key=lambda f: os.path.getmtime(os.path.join(resource.exports_dir, f)), reverse=True)
                    latest_report = report_files[0]

                    # Check if the latest report is less than 1 hour old
                    report_path = os.path.join(resource.exports_dir, latest_report)
                    mod_time = os.path.getmtime(report_path)
                    current_time = time.time()

                    # If less than 1 hour old, return the existing report
                    if current_time - mod_time < 3600:  # 3600 seconds = 1 hour
                        print(f"Using existing report: {latest_report} (generated {int((current_time - mod_time) / 60)} minutes ago)")

                        # Read the report content
                        with open(report_path.replace(".pdf", ".md"), "r", encoding="utf-8") as f:
                            summary_text = f.read()

                        # Use a relative URL instead of hardcoding the domain
                        download_url = f"/api/v1/downloads/{latest_report}"

                        task_results[task_id] = {
                            "project": project_name,
                            "days_back": days_back,
                            "summary": summary_text,
                            "download_url": download_url,
                            "cached": True,
                            "cache_age_minutes": int((current_time - mod_time) / 60)
                        }
                        task_status[task_id] = "completed"
                        return
            except Exception as e:
                error_msg = f"Error checking for existing reports: {str(e)}"
                print(error_msg)
                task_debug[task_id].append(error_msg)
                # Continue with generating a new report

        replies = []
        debug_info = task_debug[task_id]

        # Connect to Gmail
        print(f"Connecting to {Config.IMAP_SERVER}:{Config.IMAP_PORT} with account {Config.EMAIL_ACCOUNT}")
        debug_info.append(f"Connecting to {Config.IMAP_SERVER}:{Config.IMAP_PORT}")

        try:
            mail = imaplib.IMAP4_SSL(Config.IMAP_SERVER, Config.IMAP_PORT)
            debug_info.append("IMAP4_SSL connection established")
        except Exception as e:
            error_msg = f"Failed to establish IMAP connection: {str(e)}"
            print(error_msg)
            debug_info.append(error_msg)
            task_status[task_id] = "failed"
            task_results[task_id] = {"error": error_msg, "debug": debug_info}
            return

        try:
            print(f"Logging in with {Config.EMAIL_ACCOUNT}")
            mail.login(Config.EMAIL_ACCOUNT, Config.EMAIL_PASSWORD)
            debug_info.append("Login successful")
        except Exception as e:
            error_msg = f"Login error: {str(e)}"
            print(error_msg)
            debug_info.append(error_msg)
            task_status[task_id] = "failed"
            task_results[task_id] = {"error": error_msg, "debug": debug_info}
            return

        try:
            print("Selecting inbox")
            mail.select("inbox")
            debug_info.append("Inbox selected")
        except Exception as e:
            error_msg = f"Failed to select inbox: {str(e)}"
            print(error_msg)
            debug_info.append(error_msg)
            task_status[task_id] = "failed"
            task_results[task_id] = {"error": error_msg, "debug": debug_info}
            return

        date_since = (datetime.now() - timedelta(days=days_back)).strftime("%d-%b-%Y")
        search_criteria = f'(SINCE "{date_since}")'
        print(f"Searching emails with criteria: {search_criteria}")
        debug_info.append(f"Searching emails since {date_since}")

        try:
            result, data = mail.search(None, search_criteria)
            if result != "OK":
                error_msg = f"Search failed with result: {result}"
                print(error_msg)
                debug_info.append(error_msg)
                task_status[task_id] = "failed"
                task_results[task_id] = {"error": error_msg, "debug": debug_info}
                return

            message_count = len(data[0].split())
            print(f"Found {message_count} messages")
            debug_info.append(f"Found {message_count} messages")
        except Exception as e:
            error_msg = f"Search error: {str(e)}"
            print(error_msg)
            debug_info.append(error_msg)
            task_status[task_id] = "failed"
            task_results[task_id] = {"error": error_msg, "debug": debug_info}
            return

        for num in data[0].split():
            try:
                print(f"Fetching message {num}")
                result, msg_data = mail.fetch(num, "(RFC822)")
                if result != "OK":
                    print(f"Failed to fetch message {num}, skipping")
                    continue

                raw_email = msg_data[0][1]
                msg = email.message_from_bytes(raw_email)

                sender = msg["From"]
                subject = decode_header(msg["Subject"])[0][0]
                if isinstance(subject, bytes):
                    subject = subject.decode()

                print(f"Processing message: {subject}")

                # Check if this is an OPTIX-related email
                if "optix" not in subject.lower():
                    print(f"Skipping non-OPTIX email: {subject}")
                    continue

                body = ""
                if msg.is_multipart():
                    for part in msg.walk():
                        if part.get_content_type() == "text/plain" and not part.get("Content-Disposition"):
                            body = part.get_payload(decode=True).decode(errors="ignore").strip()
                            break
                else:
                    body = msg.get_payload(decode=True).decode(errors="ignore").strip()

                print(f"Adding reply from {sender}, subject: {subject}, body length: {len(body)}")
                replies.append(f"From: {sender}\nSubject: {subject}\n\n{body}")
            except Exception as e:
                print(f"Error processing message {num}: {str(e)}")
                continue

        try:
            print("Logging out from mail server")
            mail.logout()
            debug_info.append("Successfully logged out")
        except Exception as e:
            print(f"Logout error (non-critical): {str(e)}")
            debug_info.append(f"Logout warning: {str(e)}")

        print(f"Collected {len(replies)} relevant replies")
        debug_info.append(f"Collected {len(replies)} relevant replies")

        if not replies:
            task_status[task_id] = "completed"
            task_results[task_id] = {
                "message": "No relevant replies found",
                "summary": "",
                "debug": debug_info
            }
            return

        # Include WP structure from Config
        wp_task_intro = ""
        for wp_code, wp_info in Config.WP_TASK_STRUCTURE.items():
            wp_task_intro += f"## {wp_code}: {wp_info['title']}\n"
            for task_id, task_desc in wp_info["tasks"].items():
                wp_task_intro += f"- **{task_id}**: {task_desc}\n"
            wp_task_intro += "\n"

        # Summarize with Gemini
        genai.configure(api_key=Config.GEMINI_API_KEY)
        model = genai.GenerativeModel('gemini-2.0-flash')
        today_str = datetime.now().strftime("%B %d, %Y")

        prompt = (
            f"You are a professional technical writer and project coordination assistant. "
            f"You are tasked with rewriting and organizing task leader replies into a formal, structured project progress report "
            f"for the OPTIX project, dated {today_str}.\n\n"

            f"Below is the official structure of the project:\n\n"
            f"{wp_task_intro}\n"

            f"Instructions:\n"
            f"- Rewrite each task leader's reply in formal, fluent English.\n"
            f"- Keep as much detail as possible: status, problems, milestones, data issues, progress percentages, etc.\n"
            f"- Do not remove nuance or technical context.\n"
            f"- For each task, clearly indicate:\n"
            f"    • Task ID\n"
            f"    • Task title (from structure above)\n"
            f"    • Task leader's name\n"
            f"    • A bullet-style update based on their reply\n"
            f"- Group all tasks under their appropriate WP sections.\n"
            f"- Use Markdown formatting:\n"
            f"    - Use `##` for the main title\n"
            f"    - Use `###` for each WP title\n"
            f"    - Use `####` for each Task (e.g., `#### T3.2 – Demand response toolkit (Leader: Amir Vadiee)`)\n"
            f"    - Use `-` for bullet points with status updates\n"
            f"- Do not use HTML or Markdown headers like **bold** for individual sentences. Use clean structure only.\n\n"

            f"Now, rewrite and organize the following raw replies:\n"
            f"{'-'*40}\n\n"
            + "\n\n---\n\n".join(replies)
        )

        try:
            debug_info.append("Generating content with Gemini AI")
            response = model.generate_content(prompt)
            summary_text = response.text if response.parts else "Received an empty response or content was blocked."
            debug_info.append("Content generation completed")

            # Generate timestamp for the report
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_base_name = f"OPTIX_Progress_Report_{timestamp}"
            pdf_name = f"{report_base_name}.pdf"
            md_name = f"{report_base_name}.md"

            # Save paths
            pdf_path = os.path.join(resource.exports_dir, pdf_name)
            md_path = os.path.join(resource.exports_dir, md_name)

            # Save the Markdown content for caching
            with open(md_path, "w", encoding="utf-8") as f:
                f.write(summary_text)
            debug_info.append(f"Saved Markdown to {md_name}")

            # Convert Markdown to HTML
            html_content = markdown2.markdown(summary_text)

            # Generate PDF from HTML
            debug_info.append("Generating PDF")
            pdfkit.from_string(html_content, pdf_path, options={
                'page-size': 'A4',
                'encoding': "UTF-8",
                'margin-top': '0.75in',
                'margin-right': '0.75in',
                'margin-bottom': '0.75in',
                'margin-left': '0.75in'
            })
            debug_info.append(f"Saved PDF to {pdf_name}")

            # Use a relative URL instead of hardcoding the domain
            download_url = f"/api/v1/downloads/{pdf_name}"

            task_results[task_id] = {
                "project": project_name,
                "days_back": days_back,
                "summary": summary_text,
                "download_url": download_url,
                "cached": False
            }
            task_status[task_id] = "completed"

        except Exception as e:
            error_msg = f"Error in content generation or PDF creation: {str(e)}"
            print(error_msg)
            debug_info.append(error_msg)
            task_status[task_id] = "failed"
            task_results[task_id] = {"error": error_msg, "debug": debug_info}
            return

    except Exception as e:
        error_msg = f"Error in async report generation: {str(e)}"
        print(error_msg)
        if task_id in task_debug:
            task_debug[task_id].append(error_msg)
        else:
            task_debug[task_id] = [error_msg]
        task_status[task_id] = "failed"
        task_results[task_id] = {"error": error_msg, "debug": task_debug.get(task_id, [])}
    finally:
        # Reset the global flag to indicate that report generation is no longer in progress
        report_generation_in_progress = False
        current_report_task_id = None
        return

class OptixResource(MethodView):
    """Resource class for OPTIX project management endpoints."""

    def __init__(self):
        """Initialize the OptixResource."""
        self.exports_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "static", "exports"))
        os.makedirs(self.exports_dir, exist_ok=True)

    def _send_email(self, recipient_email, recipient_name, subject, body):
        """
        Send an email to a recipient.

        Args:
            recipient_email (str): The recipient's email address.
            recipient_name (str): The recipient's name.
            subject (str): The email subject.
            body (str): The email body.

        Returns:
            bool: True if the email was sent successfully, False otherwise.
        """
        try:
            msg = MIMEText(body, "plain", "utf-8")
            msg["Subject"] = subject
            msg["From"] = Config.EMAIL_ACCOUNT
            msg["To"] = recipient_email

            with smtplib.SMTP(Config.SMTP_SERVER, Config.SMTP_PORT) as server:
                server.starttls()
                server.login(Config.EMAIL_ACCOUNT, Config.EMAIL_PASSWORD)
                server.sendmail(Config.EMAIL_ACCOUNT, [recipient_email], msg.as_string())
            return True
        except Exception as e:
            print(f"Error sending email to {recipient_email}: {str(e)}")
            return False

    def send_task_update_request(self):
        """
        Send a task update request to all task leaders.

        Returns:
            A JSON response with the result of the operation.
        """
        try:
            password = request.form.get("password")
            custom_message = request.form.get("message", "").strip()

            if password != Config.PM_AUTH_PASSWORD:
                return {"error": "Unauthorized. Only the project manager can request task updates."}, 403

            if not custom_message:
                return {"error": "Missing message body"}, 400

            sent = []
            failed = []

            for email, info in Config.TASK_LEADER_EMAILS.items():
                name = info["name"]
                tasks = info["tasks"]

                subject = f"OPTIX Project Update Request – Task(s) {', '.join(tasks)}"
                body = re.sub(r"(Dear\s+)(.*?)(,)", rf"\1{name}\3", custom_message, count=1)

                if self._send_email(email, name, subject, body):
                    sent.append(email)
                else:
                    failed.append({"email": email, "error": "Failed to send email"})

            return {
                "message": "Emails processed.",
                "sent": sent,
                "failed": failed
            }

        except Exception as e:
            return {"error": str(e)}, 500

    def send_email(self):
        """
        Send an email to one or more recipients.

        This endpoint accepts the following parameters:
        - recipients: A list of email addresses to send the email to
        - subject: The subject of the email
        - message: The body of the email
        - password: Optional authentication password (required if auth_required is True)
        - auth_required: Whether authentication is required (default: False)

        Returns:
            A JSON response with the result of the operation.
        """
        try:
            # Check if the request is JSON or form data
            if request.is_json:
                data = request.get_json()
                recipients = data.get("recipients", [])
                subject = data.get("subject", "")
                message = data.get("message", "")
                password = data.get("password", "")
            else:
                # Handle form data
                recipients = request.form.getlist("recipients")
                subject = request.form.get("subject", "")
                message = request.form.get("message", "")
                password = request.form.get("password", "")

            # Validate required fields
            if not recipients:
                return {"error": "Missing recipients. Please provide at least one email address."}, 400

            if not subject:
                return {"error": "Missing subject. Please provide an email subject."}, 400

            if not message:
                return {"error": "Missing message. Please provide an email body."}, 400

            # Check authentication if required
            if password != Config.PM_AUTH_PASSWORD:
                return {"error": "Unauthorized. Password required to send emails."}, 403

            # Ensure recipients is a list
            if isinstance(recipients, str):
                recipients = [recipients]

            sent = []
            failed = []

            # Send email to each recipient
            for recipient_email in recipients:
                # Try to find the recipient's name in the task leaders list
                recipient_name = "Recipient"
                for email, info in Config.TASK_LEADER_EMAILS.items():
                    if email.lower() == recipient_email.lower():
                        recipient_name = info["name"]
                        break

                # For CC recipients
                for cc in Config.REPORT_DISTRIBUTION_CC:
                    if cc["email"].lower() == recipient_email.lower():
                        recipient_name = cc["name"]
                        break

                # Send the email
                if self._send_email(recipient_email, recipient_name, subject, message.strip()):
                    sent.append(recipient_email)
                else:
                    failed.append({"email": recipient_email, "error": "Failed to send email"})

            # Return the results
            return {
                "message": f"Email sent to {len(sent)} recipients. Failed to send to {len(failed)} recipients.",
                "subject": subject,
                "sent": sent,
                "failed": failed
            }

        except Exception as e:
            error_msg = f"Error sending email: {str(e)}"
            print(error_msg)
            return {"error": error_msg}, 500

    def send_task_specific_message(self):
        """
        DEPRECATED: Use send_email instead.
        Send a message to specific task leaders.

        Returns:
            A JSON response with the result of the operation.
        """
        try:
            password = request.form.get("password")

            if password != Config.PM_AUTH_PASSWORD:
                return {"error": "Unauthorized. Only the project manager can send direct task messages."}, 403

            task_ids = request.form.getlist("task_ids")
            message = request.form.get("message")

            if not task_ids or not message:
                return {"error": "Missing task_ids or message"}, 400

            email_task_map = {}
            for email, info in Config.TASK_LEADER_EMAILS.items():
                matched_tasks = [task for task in info["tasks"] if task in task_ids]
                if matched_tasks:
                    email_task_map[email] = {"name": info["name"], "tasks": matched_tasks}

            if not email_task_map:
                return {"error": "No matching task leaders found for given task_ids"}, 404

            sent = []
            failed = []

            for email, info in email_task_map.items():
                name = info["name"]
                tasks = info["tasks"]

                subject = f"OPTIX Task Update Request – {', '.join(tasks)}"
                body = message.strip()

                if self._send_email(email, name, subject, body):
                    sent.append(email)
                else:
                    failed.append({"email": email, "error": "Failed to send email"})

            return {
                "message": "Custom messages processed.",
                "tasks": task_ids,
                "sent": sent,
                "failed": failed
            }

        except Exception as e:
            return {"error": str(e)}, 500

    def generate_task_status_report(self):
        """
        Generate a task status report from email replies.

        If a report has been generated within the last hour, returns the existing report.
        Otherwise, starts an asynchronous task to generate the report and returns a message
        indicating that the report is being generated.

        Returns:
            A JSON response with either the report data (if cached) or a message.
        """
        project_name = request.form.get("project_name")
        days_back = int(request.form.get("days_back", 5))
        force_refresh = request.form.get("force_refresh", "false").lower() in ["true", "1", "yes"]

        if not project_name:
            return {"error": "Missing 'project_name'"}, 400

        # Check if a report has been generated within the last hour
        if not force_refresh:
            try:
                # Get all PDF files in the exports directory
                report_files = [f for f in os.listdir(self.exports_dir)
                               if f.startswith("OPTIX_Progress_Report_") and f.endswith(".pdf")]

                if report_files:
                    # Sort by modification time (newest first)
                    report_files.sort(key=lambda f: os.path.getmtime(os.path.join(self.exports_dir, f)), reverse=True)
                    latest_report = report_files[0]

                    # Check if the latest report is less than 1 hour old
                    report_path = os.path.join(self.exports_dir, latest_report)
                    mod_time = os.path.getmtime(report_path)
                    current_time = time.time()

                    # If less than 1 hour old, return the existing report
                    if current_time - mod_time < 3600:  # 3600 seconds = 1 hour
                        print(f"Using existing report: {latest_report} (generated {int((current_time - mod_time) / 60)} minutes ago)")

                        # Read the report content
                        with open(report_path.replace(".pdf", ".md"), "r", encoding="utf-8") as f:
                            summary_text = f.read()

                        # Use a relative URL instead of hardcoding the domain
                        download_url = f"/api/v1/downloads/{latest_report}"

                        return {
                            "project": project_name,
                            "days_back": days_back,
                            "summary": summary_text,
                            "download_url": download_url,
                            "cached": True,
                            "cache_age_minutes": int((current_time - mod_time) / 60)
                        }
            except Exception as e:
                print(f"Error checking for existing reports: {str(e)}")
                # Continue with generating a new report

        # Check if a report generation is already in progress
        if report_generation_in_progress:
            # If a report is already being generated, return information about the current task
            message = "A report is already being generated. Please wait for it to complete before requesting a new one."

            if current_report_task_id and current_report_task_id in task_status:
                status = task_status[current_report_task_id]
                message += f" Current status: {status}"

                # If the task is completed, suggest checking the latest report
                if status == "completed":
                    message = "A report was recently generated. Please use the /api/v1/check-latest-report endpoint to view it."

            return {
                "message": message,
                "summary": "Another report generation is in progress. Please wait...",
                "in_progress": True
            }

        # Generate a unique task ID
        task_id = str(uuid.uuid4())

        # Initialize task status
        task_status[task_id] = "pending"
        task_debug[task_id] = ["Task created, starting background processing"]

        # Start the report generation in a background thread
        thread = threading.Thread(
            target=generate_report_async,
            args=(task_id, self, project_name, days_back, force_refresh)
        )
        thread.daemon = True  # Thread will exit when the main program exits
        thread.start()

        # Return a message that will be displayed to the user
        return {
            "message": "The report is being generated in the background. This may take a few minutes. Please check back in about a minute to see the completed report.",
            "summary": "Report generation in progress. Please wait...",
            "task_id": task_id  # Include the task ID for reference
        }

    def send_report_link(self):
        """
        Send a report link to all task leaders and project administrators.

        Returns:
            A JSON response with the result of the operation.
        """
        try:
            password = request.form.get("password")
            if password != Config.PM_AUTH_PASSWORD:
                return {
                    "error": "Unauthorized. Only the project manager can send the report link."
                }, 403

            report_link = request.form.get("report_link")

            if not report_link:
                return {"error": "Missing report_link"}, 400

            sent = []
            failed = []
            today_str = datetime.now().strftime("%B %d, %Y")

            # Collect recipients: task leaders + project admin(s)
            all_recipients = {
                email: info["name"] for email, info in Config.TASK_LEADER_EMAILS.items()
            }
            for cc in Config.REPORT_DISTRIBUTION_CC:
                all_recipients[cc["email"]] = cc["name"]

            for email, name in all_recipients.items():
                subject = "OPTIX Project – Task Progress Report Available"

                body = (
                    f"Dear {name},\n\n"
                    f"The latest OPTIX Project Progress Report is now available as of {today_str}.\n\n"
                    f"This report summarizes recent developments across all work packages, including updates from WP1 through WP6.\n\n"
                    f"You can download the report here:\n📄 {report_link}\n\n"
                    f"Please review the report and align any upcoming activities or milestones accordingly.\n\n"
                    f"Best regards,\nOPTIX Coordination Team"
                )

                if self._send_email(email, name, subject, body):
                    sent.append(email)
                else:
                    failed.append({"email": email, "error": "Failed to send email"})

            return {
                "message": "Report link sent successfully.",
                "sent": sent,
                "failed": failed
            }

        except Exception as e:
            return {"error": str(e)}, 500

    def download_file(self, filename):
        """
        Download a file from the exports directory.

        Args:
            filename (str): The name of the file to download.

        Returns:
            The file as an attachment.
        """
        return send_from_directory(
            directory=self.exports_dir,
            path=filename,
            as_attachment=True,
            mimetype="application/pdf" if filename.endswith(".pdf") else "text/csv"
        )

    def check_latest_report(self):
        """
        Check if a new report has been completed.

        This endpoint is used to check if any reports have been completed recently.
        It returns the most recent report if one exists.

        Returns:
            A JSON response with the latest report data or a message.
        """
        try:
            # Check if a report generation is in progress
            response = {}
            if report_generation_in_progress:
                response["report_generation_in_progress"] = True
                if current_report_task_id and current_report_task_id in task_status:
                    response["current_task_status"] = task_status[current_report_task_id]
                    response["message"] = f"A report is currently being generated. Status: {task_status[current_report_task_id]}"
            else:
                response["report_generation_in_progress"] = False

            # Get all PDF files in the exports directory
            report_files = [f for f in os.listdir(self.exports_dir)
                           if f.startswith("OPTIX_Progress_Report_") and f.endswith(".pdf")]

            if not report_files:
                response.update({
                    "message": "No reports found. Please generate a report first.",
                    "summary": "No reports available."
                })
                return response

            # Sort by modification time (newest first)
            report_files.sort(key=lambda f: os.path.getmtime(os.path.join(self.exports_dir, f)), reverse=True)
            latest_report = report_files[0]

            # Get the report path and modification time
            report_path = os.path.join(self.exports_dir, latest_report)
            mod_time = os.path.getmtime(report_path)
            current_time = time.time()

            # Read the report content
            with open(report_path.replace(".pdf", ".md"), "r", encoding="utf-8") as f:
                summary_text = f.read()

            # Use a relative URL instead of hardcoding the domain
            download_url = f"/api/v1/downloads/{latest_report}"

            response.update({
                "summary": summary_text,
                "download_url": download_url,
                "timestamp": datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S'),
                "age_minutes": int((current_time - mod_time) / 60)
            })

            return response

        except Exception as e:
            error_msg = f"Error checking for latest report: {str(e)}"
            print(error_msg)
            return {"error": error_msg}, 500

    def check_task_status(self, task_id):
        """
        Check the status of an asynchronous task.

        Args:
            task_id (str): The unique ID of the task to check.

        Returns:
            A JSON response with the task status and results if completed.
        """
        if task_id not in task_status:
            return {"error": "Task not found"}, 404

        status = task_status[task_id]
        response = {"task_id": task_id, "status": status}

        # If the task is completed or failed, include the results
        if status in ["completed", "failed"]:
            if task_id in task_results:
                response["results"] = task_results[task_id]

            # Include debug info if available
            if task_id in task_debug:
                response["debug"] = task_debug[task_id]

        return response

    def delete_all_reports(self):
        """
        Delete all existing reports from the exports directory.

        Requires authentication with the PM_AUTH_PASSWORD.

        Returns:
            A JSON response with the result of the operation.
        """
        try:
            # Check authentication
            password = request.form.get("password")
            if password != Config.PM_AUTH_PASSWORD:
                return {"error": "Unauthorized. Authentication required to delete reports."}, 403

            # Get all report files (both PDF and MD)
            report_files = [f for f in os.listdir(self.exports_dir)
                           if f.startswith("OPTIX_Progress_Report_") and
                           (f.endswith(".pdf") or f.endswith(".md"))]

            if not report_files:
                return {"message": "No reports found to delete", "deleted_count": 0}

            # Delete each file
            deleted_count = 0
            for filename in report_files:
                file_path = os.path.join(self.exports_dir, filename)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    print(f"Deleted: {file_path}")
                except Exception as e:
                    print(f"Error deleting {file_path}: {str(e)}")

            return {
                "message": f"Successfully deleted {deleted_count} report files",
                "deleted_count": deleted_count,
                "deleted_files": report_files
            }

        except Exception as e:
            error_msg = f"Error deleting reports: {str(e)}"
            print(error_msg)
            return {"error": error_msg}, 500