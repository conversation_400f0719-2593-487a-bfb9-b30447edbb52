"""
API routes module.

This module registers all API routes with the Flask application.
"""

from flask import Blueprint
from api.resources.policy import PolicyResource
from api.resources.optix import OptixResource
from utils.session_manager import get_session, enable_auto_confirm, disable_auto_confirm, get_auto_confirm_status
import os

# Directory for exported files
EXPORTS_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "static", "exports"))
os.makedirs(EXPORTS_DIR, exist_ok=True)

def register_routes(app):
    """
    Register all API routes with the Flask application.

    Args:
        app: The Flask application instance.

    Returns:
        bool: True if routes were registered successfully.
    """
    # Create the blueprint
    blueprint = Blueprint("api_v1", __name__, url_prefix="/api/v1")

    # Initialize resources
    policy_resource = PolicyResource()
    optix_resource = OptixResource()

    # Policy routes
    blueprint.add_url_rule("/policies", view_func=policy_resource.get, methods=["GET"])
    blueprint.add_url_rule("/policies/<int:policy_id>", view_func=policy_resource.get, methods=["GET"])

    # SQL query endpoint
    @blueprint.route("/sql", methods=["GET", "POST"])
    def sql_query():
        from flask import request
        session_id = request.args.get('session_id', 'default')

        if request.method == "POST":
            data = request.get_json()
            print(f"POST request data: {data}")
            if not data or 'query' not in data:
                return {"error": "No SQL query provided", "debug": {"received_data": data}}, 400
            sql_query = data['query']
        else:  # GET method
            sql_query = request.args.get('query')
            print(f"GET request query param: {sql_query}")
            if not sql_query:
                return {"error": "No SQL query provided", "debug": {"query_params": dict(request.args)}}, 400

        print(f"Executing SQL: {sql_query}")
        try:
            result = policy_resource.execute_sql(sql_query, session_id)
            return result
        except Exception as e:
            print(f"SQL execution error: {str(e)}")
            return {"error": str(e), "debug": {"query": sql_query, "session_id": session_id}}, 400

    # Session management endpoints
    @blueprint.route("/settings/auto-confirm", methods=["GET"])
    def get_auto_confirm():
        """Get the current auto-confirm setting"""
        from flask import request, jsonify
        session_id = request.args.get('session_id', 'default')
        status = get_auto_confirm_status(session_id)
        return jsonify({
            "auto_confirm_sql": status,
            "message": f"Auto-confirm for SQL queries is {'enabled' if status else 'disabled'}"
        })

    @blueprint.route("/settings/auto-confirm/enable", methods=["POST"])
    def set_auto_confirm_enabled():
        """Enable auto-confirm for SQL queries"""
        from flask import request, jsonify
        session_id = request.args.get('session_id', 'default')
        session = enable_auto_confirm(session_id)
        return jsonify({
            "auto_confirm_sql": True,
            "message": "Auto-confirm for SQL queries has been enabled"
        })

    @blueprint.route("/settings/auto-confirm/disable", methods=["POST"])
    def set_auto_confirm_disabled():
        """Disable auto-confirm for SQL queries"""
        from flask import request, jsonify
        session_id = request.args.get('session_id', 'default')
        session = disable_auto_confirm(session_id)
        return jsonify({
            "auto_confirm_sql": False,
            "message": "Auto-confirm for SQL queries has been disabled"
        })

    # OPTIX project management routes
    @blueprint.route("/send-task-update-request", methods=["POST"])
    def send_task_update_request():
        return optix_resource.send_task_update_request()

    @blueprint.route("/send-email", methods=["POST"])
    def send_email():
        """Send an email to one or more recipients."""
        return optix_resource.send_email()

    @blueprint.route("/generate-task-status-report", methods=["POST"])
    def generate_task_status_report():
        return optix_resource.generate_task_status_report()

    @blueprint.route("/check-latest-report", methods=["GET"])
    def check_latest_report():
        """Check if a new report has been completed."""
        return optix_resource.check_latest_report()

    @blueprint.route("/send-report-link", methods=["POST"])
    def send_report_link_to_all():
        return optix_resource.send_report_link()

    # File download routes
    @blueprint.route("/downloads/<filename>", methods=["GET"])
    def download_file(filename):
        return optix_resource.download_file(filename)

    # Task report form route
    @blueprint.route("/task-report", methods=["GET"])
    def task_report_form():
        """Serve the task report form HTML page."""
        from flask import send_from_directory
        return send_from_directory('static', 'task_report_form.html')

    # Task status check route
    @blueprint.route("/task-status/<task_id>", methods=["GET"])
    def check_task_status(task_id):
        """Check the status of an asynchronous task."""
        return optix_resource.check_task_status(task_id)

    # Delete all reports route
    @blueprint.route("/delete-all-reports", methods=["POST"])
    def delete_all_reports():
        """Delete all existing reports."""
        return optix_resource.delete_all_reports()

    # Register the blueprint with the app
    app.register_blueprint(blueprint)

    # Debug output of all registered routes
    print("All registered routes:")
    for rule in app.url_map.iter_rules():
        print(f"{rule.endpoint}: {rule.rule} [{', '.join(rule.methods)}]")

    return True
