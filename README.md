# GPT Agent Platform

A Flask-based API service for creating and deploying custom GPT agents. This platform provides a foundation for building AI assistants with specific capabilities, including the OPTIX project management functionality as one implementation.

## Features

- Create custom GPT agents with specialized knowledge and capabilities
- RESTful API architecture for easy integration with other systems
- Asynchronous task processing for long-running operations
- Email communication capabilities
- PDF report generation
- Policy data management and querying
- Extensible architecture for adding new agent capabilities

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/xiufengliu/gpt-agent.git
   cd gpt-agent
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Configure the application:
   - Copy `config.py` to `config_local.py` and update with your settings
   - Set up email credentials and other configuration options

4. Run the application:
   ```
   python app.py
   ```

   Or use the provided shell scripts:
   ```
   ./run.sh
   ```

## API Endpoints

### Core Agent Functionality

- `/api/v1/send-email` - Send an email to one or more recipients
- `/api/v1/task-status/<task_id>` - Check the status of an asynchronous task

### OPTIX Project Management (Example Implementation)

- `/api/v1/send-task-update-request` - Send task update requests to all task leaders
- `/api/v1/generate-task-status-report` - Generate a task status report from email replies
- `/api/v1/check-latest-report` - Check if a new report has been completed
- `/api/v1/send-report-link` - Send a report link to all task leaders
- `/api/v1/delete-all-reports` - Delete all existing reports (requires authentication)

### Data Management

- `/api/v1/policies` - Get all policies with optional filters
- `/api/v1/policies/<policy_id>` - Get policy by ID
- `/api/v1/sql` - Execute custom SQL queries

### Settings

- `/api/v1/settings/auto-confirm` - Get auto-confirm status
- `/api/v1/settings/auto-confirm/enable` - Enable auto-confirm
- `/api/v1/settings/auto-confirm/disable` - Disable auto-confirm

## Extending the Platform

This platform is designed to be extended with new capabilities. To add new functionality:

1. Create new resource classes in the `api/resources` directory
2. Register new routes in `api/routes.py`
3. Implement business logic in the `services` directory
4. Add models as needed in the `models` directory

## License

MIT License

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
