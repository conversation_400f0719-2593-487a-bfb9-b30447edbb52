#!/bin/bash
# Run script for the REST API application
#
# This script cleans Python cache and runs the Flask application.

# Navigate to script directory
cd "$(dirname "$0")"

# Clean up __pycache__ directories to ensure fresh code is used
echo "Cleaning Python cache..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete

# Set Python path
export PYTHONPATH="$(pwd)"
echo "Python path set to: $PYTHONPATH"

# Create necessary directories
mkdir -p static/exports

# Run the application
echo "Starting Flask API server..."
python run.py
